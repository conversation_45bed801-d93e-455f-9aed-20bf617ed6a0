'use client';

import Header from '../../components/layout/Header';
import Footer from '../../components/layout/Footer';
import Carousel from '../../components/ui/Carousel';
import Button from '../../components/ui/Button';
import Image from 'next/image';

export default function OcchialiSoleContent() {
  const brands = [
    { id: '1', src: '/images/marchi/occhiali/rayban-logo.jpg', alt: 'Ray-Ban' },
    { id: '2', src: '/images/marchi/occhiali/persol-logo.jpg', alt: 'Persol' },
    { id: '3', src: '/images/marchi/occhiali/vogue-logo.jpg', alt: 'Vogue' },
    { id: '4', src: '/images/marchi/occhiali/Karl<PERSON>feld-logo.jpg', alt: '<PERSON>' },
    { id: '5', src: '/images/marchi/occhiali/Dsquared2-logo.jpg', alt: 'Dsquared2' },
    { id: '6', src: '/images/marchi/occhiali/<PERSON>-logo.jpg', alt: '<PERSON>' },
    { id: '7', src: '/images/marchi/occhiali/INVU-logo.jpg', alt: 'INVU' },
    { id: '8', src: '/images/marchi/occhiali/Borbonese-logo.jpg', alt: 'Borbonese' },
    { id: '9', src: '/images/marchi/occhiali/cafenoir-logo.jpg', alt: 'Café Noir' },
    { id: '10', src: '/images/marchi/occhiali/OldSchool-logo.jpg', alt: 'Old School' },
    { id: '11', src: '/images/marchi/occhiali/Tipidiversi-logo.jpg', alt: 'Tipi Diversi' },
    { id: '12', src: '/images/marchi/occhiali/Danshari-logo.jpg', alt: 'Danshari' },
  ];

  const occhialiSoleImages = [
    {
      src: '/images/occhiali-sole/DSC09432.jpeg',
      alt: 'Collezione occhiali da sole eleganti',
    },
    {
      src: '/images/occhiali-sole/DSC09476.jpeg',
      alt: 'Occhiali da sole di design e qualità',
    },
    {
      src: '/images/occhiali-sole/DSC09553.jpeg',
      alt: 'Ampia selezione di occhiali da sole',
    },
    {
      src: '/images/occhiali-sole/DSC09573.jpeg',
      alt: 'Occhiali da sole per ogni stile',
    },
  ];

  const features = [
    {
      title: 'Protezione UV 100%',
      description: 'Lenti certificate che filtrano completamente i raggi UVA e UVB per la massima protezione.',
      icon: '☀️'
    },
    {
      title: 'Lenti Polarizzate',
      description: 'Eliminano i riflessi e migliorano la nitidezza per una visione ottimale.',
      icon: '🌊'
    },
    {
      title: 'Montature Premium',
      description: 'Materiali leggeri ma resistenti, progettati per durare nel tempo.',
      icon: '💎'
    },
    {
      title: 'Brand Internazionali',
      description: 'Selezione dei migliori marchi mondiali per stile e affidabilità.',
      icon: '🌟'
    },
    {
      title: 'Occhiali Graduati',
      description: 'Soluzioni su misura anche per occhiali da sole con correzione visiva.',
      icon: '👓'
    },
    {
      title: 'Consulenza Esperta',
      description: 'Ti aiutiamo a scegliere il modello perfetto per il tuo viso e stile.',
      icon: '👨‍⚕️'
    }
  ];

  return (
    <div className="min-h-screen">
      <Header />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-16 bg-gradient-to-b from-primary/5 to-background">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <p className="text-accent text-lg font-sans font-medium mb-4">
                  Protezione e Stile
                </p>
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold font-sans text-text-base mb-6">
                  Occhiali da Sole
                </h1>
                <p className="text-xl text-text-base opacity-80 leading-relaxed mb-8">
                  Proteggi i tuoi occhi con stile. Scopri la nostra collezione di occhiali da sole di alta qualità con protezione UV 100% e design esclusivi dei migliori brand internazionali.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={() => window.location.href = '/contatti'}
                  >
                    Scopri la Collezione
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={() => window.location.href = '/esami-vista'}
                  >
                    Controllo Vista
                  </Button>
                </div>
              </div>
              <div className="relative h-96 lg:h-[500px] rounded-lg overflow-hidden shadow-xl">
                <Image
                  src="/images/section-cards/DSC09553.jpeg"
                  alt="Occhiali da sole di qualità"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Occhiali da Sole Gallery */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold font-sans text-text-base mb-4">
                La Nostra Collezione
              </h2>
              <p className="text-lg text-text-base opacity-80 max-w-2xl mx-auto">
                Scopri la nostra selezione di occhiali da sole per uomo, donna e bambino
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
              {occhialiSoleImages.map((image, index) => (
                <div key={index} className="relative h-64 rounded-lg overflow-hidden shadow-lg group">
                  <Image
                    src={image.src}
                    alt={image.alt}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300" />
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Main Content Section */}
        <section className="py-16 bg-background">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold font-sans text-text-base mb-8 text-center">
                Occhiali da Sole: Protezione, Qualità e Stile Senza Compromessi
              </h2>
              
              <div className="prose prose-lg max-w-none text-text-base opacity-90 leading-relaxed space-y-6">
                <p>
                  La salute dei tuoi occhi merita il massimo, ogni giorno. Gli occhiali da sole non sono soltanto un accessorio di tendenza: sono un presidio fondamentale per proteggere la vista dai danni causati dai raggi UV, uno dei principali fattori di rischio per l'invecchiamento precoce dell'occhio e per patologie come cataratta e maculopatie.
                </p>
                
                <p>
                  Nel nostro punto vendita selezioniamo esclusivamente occhiali da sole di alta qualità, realizzati con lenti certificate in grado di filtrare il 100% dei raggi UVA e UVB. Che si tratti di una giornata al mare, di un'escursione in montagna o della semplice vita quotidiana in città, offriamo soluzioni studiate per garantire la massima protezione e il miglior comfort visivo in ogni situazione.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h3 className="text-2xl md:text-3xl font-bold font-sans text-text-base mb-6">
                Le nostre collezioni comprendono:
              </h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
              {features.map((feature, index) => (
                <div key={index} className="text-center p-6 bg-background rounded-lg hover:shadow-lg transition-shadow duration-300">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h4 className="text-xl font-semibold font-sans text-text-base mb-3">
                    {feature.title}
                  </h4>
                  <p className="text-text-base opacity-80 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>

            <div className="max-w-4xl mx-auto">
              <div className="prose prose-lg max-w-none text-text-base opacity-90 leading-relaxed space-y-6">
                <ul className="space-y-4 text-lg">
                  <li className="flex items-start">
                    <span className="text-accent mr-3 mt-1">•</span>
                    <span><strong>Occhiali con lenti polarizzate</strong>, ideali per eliminare i riflessi e migliorare la nitidezza;</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-accent mr-3 mt-1">•</span>
                    <span><strong>Montature leggere ma robuste</strong>, progettate per durare nel tempo senza rinunciare all'eleganza;</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-accent mr-3 mt-1">•</span>
                    <span><strong>Modelli dei migliori brand internazionali</strong>, sinonimo di stile e affidabilità;</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-accent mr-3 mt-1">•</span>
                    <span><strong>Soluzioni su misura</strong> anche per chi necessita di occhiali da sole graduati.</span>
                  </li>
                </ul>

                <p className="text-center text-lg font-medium text-primary mt-8">
                  Ogni occhiale viene scelto per rispondere a un principio fondamentale: coniugare estetica e benessere visivo. Perché difendere la vista oggi significa investire in una qualità di vita migliore domani.
                </p>

                <p>
                  Affidati alla nostra esperienza per trovare il modello più adatto al tuo viso, al tuo stile e alle tue esigenze visive.
                  Vieni a scoprire la nostra selezione di occhiali da sole da uomo, donna e bambino: ti aspetta un mondo di protezione e design senza compromessi.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Brands Section */}
        <section className="py-16 bg-background">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold font-sans text-text-base mb-4">
                I Nostri Brand
              </h2>
              <p className="text-lg text-text-base opacity-80 max-w-2xl mx-auto">
                Collaboriamo con i migliori marchi internazionali per offrirti occhiali da sole di qualità superiore
              </p>
            </div>
            
            {brands.length > 0 && (
              <div className="max-w-6xl mx-auto">
                <Carousel
                  items={brands}
                  autoPlay={true}
                  autoPlayInterval={3500}
                  showDots={true}
                  showArrows={true}
                  itemsPerView={4}
                  className="brands-carousel carousel-container"
                />
              </div>
            )}
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-primary text-white">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl md:text-4xl font-bold font-sans mb-6">
              Scopri la Nostra Collezione
            </h2>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              Vieni a trovarci per scoprire la collezione completa di occhiali da sole. 
              I nostri esperti ti aiuteranno a trovare il modello perfetto per te.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="accent"
                size="lg"
                onClick={() => window.location.href = '/contatti'}
              >
                Vieni in Negozio
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => window.location.href = 'tel:06123456789'}
                className="border-white text-white hover:bg-white hover:text-primary"
              >
                Chiama: 06 123 456 789
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
}
