'use client';

import Image from 'next/image';
import Button from '../../components/ui/Button';

export default function ServiziContent() {
  const servicesImages = [
    {
      src: '/images/servizi/DSC09581.jpeg',
      alt: 'Strumentazione professionale per esami della vista',
    },
    {
      src: '/images/servizi/DSC09591.jpeg',
      alt: 'Apparecchiature diagnostiche avanzate',
    },
    {
      src: '/images/servizi/servizio.jpeg',
      alt: 'Laboratorio e strumenti di precisione',
    },
    {
      src: '/images/servizi/DSC09607.jpeg',
      alt: 'Area consulenza e servizi specializzati',
    },
  ];

  const mainServices = [
    {
      title: 'Misurazione Optometrica',
      description: 'La misurazione optometrica è il cuore della nostra consulenza visiva. Attraverso strumenti all\'avanguardia e la competenza dei nostri ottici optometristi, determiniamo con precisione le tue diottrie e l\'eventuale presenza di difetti visivi come miopia, ipermetropia, astigmatismo o presbiopia. Questo esame approfondito ci permette di prescrivere le lenti più adatte alle tue specifiche esigenze, garantendo una visione nitida e confortevole in ogni situazione.',
      icon: '👁️'
    },
    {
      title: 'Tonometria',
      description: 'La tonometria è un esame rapido e indolore che misura la pressione intraoculare. È un controllo fondamentale per la prevenzione e il monitoraggio del glaucoma, una patologia che, se non diagnosticata e trattata per tempo, può compromettere seriamente la vista. Grazie a questo servizio, possiamo intercettare precocemente eventuali anomalie e consigliarti i passi successivi per la cura dei tuoi occhi.',
      icon: '📊'
    },
    {
      title: 'Pachimetria',
      description: 'La pachimetria è una tecnica non invasiva che misura lo spessore della cornea. Questo dato è cruciale, specialmente in presenza di pressione oculare elevata, poiché lo spessore corneale può influenzare l\'accuratezza delle misurazioni della pressione. È un esame essenziale per una diagnosi più precisa e per valutare l\'idoneità a certi trattamenti o interventi.',
      icon: '📏'
    },
    {
      title: 'Cheratometria',
      description: 'La cheratometria misura la curvatura della superficie anteriore della cornea. Questo esame è indispensabile per l\'applicazione delle lenti a contatto, poiché ci permette di scegliere la lente con la curvatura più adatta al tuo occhio, assicurando comfort e una visione ottimale. È inoltre utile per la diagnosi e il monitoraggio di patologie corneali come il cheratocono.',
      icon: '📐'
    }
  ];

  const additionalServices = [
    {
      title: 'Amplifon Point',
      description: 'Siamo Amplifon Point, il tuo punto di riferimento per l\'udito. Presso il nostro negozio puoi trovare consulenza specializzata, effettuare test gratuiti dell\'udito e scoprire le soluzioni acustiche più innovative e adatte a te. Un servizio comodo e professionale per prenderti cura anche del tuo benessere uditivo.',
      icon: '🔊'
    },
    {
      title: 'Foto Tessera',
      description: 'Hai bisogno di foto per documenti d\'identità, passaporti, patenti o visti? Offriamo un servizio rapido e professionale di foto tessera, realizzate secondo le normative vigenti per tutti i tipi di documenti. Un modo veloce e comodo per ottenere le tue foto senza stress.',
      icon: '📷'
    },
    {
      title: 'Laboratorio Riparazioni Occhiali',
      description: 'Il nostro laboratorio interno è specializzato nella riparazione e manutenzione dei tuoi occhiali da vista e da sole. Che si tratti di una montatura rotta, una vite allentata o una lente da sostituire, il nostro team esperto è pronto a intervenire con precisione e rapidità per ridare vita ai tuoi occhiali, garantendoti di non rimanere mai senza il tuo prezioso ausilio visivo.',
      icon: '🔧'
    }
  ];

  return (
    <>
      {/* Hero Section */}
      <section className="py-16 bg-gradient-to-b from-primary/5 to-background">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold font-sans text-text-base mb-6">
              I Nostri <span className="text-primary">Servizi</span>
            </h1>
            <p className="text-xl md:text-2xl text-text-base opacity-80 leading-relaxed">
              Offriamo una gamma completa di servizi dedicati alla salute e al benessere dei tuoi occhi, 
              unendo l'esperienza quarantennale di Ottica GR1 alle tecnologie più avanzate.
            </p>
          </div>
        </div>
      </section>

      {/* Services Gallery */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {servicesImages.map((image, index) => (
              <div key={index} className="relative h-64 rounded-lg overflow-hidden shadow-lg group">
                <Image
                  src={image.src}
                  alt={image.alt}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300" />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Main Services Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold font-sans text-text-base mb-4">
              Servizi Specialistici
            </h2>
            <p className="text-lg text-text-base opacity-80 max-w-2xl mx-auto">
              Tecnologie avanzate e competenza professionale per la cura della tua vista
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {mainServices.map((service, index) => (
              <div key={index} className="bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                <div className="flex items-start space-x-4">
                  <div className="text-4xl flex-shrink-0">{service.icon}</div>
                  <div>
                    <h3 className="text-2xl font-semibold font-sans text-text-base mb-4">
                      {service.title}
                    </h3>
                    <p className="text-text-base opacity-80 leading-relaxed">
                      {service.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Additional Services Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold font-sans text-text-base mb-4">
              Servizi Aggiuntivi
            </h2>
            <p className="text-lg text-text-base opacity-80 max-w-2xl mx-auto">
              Oltre alla cura della vista, offriamo anche servizi pratici per le tue esigenze quotidiane
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {additionalServices.map((service, index) => (
              <div key={index} className="bg-background p-6 rounded-lg hover:shadow-lg transition-shadow duration-300">
                <div className="text-center">
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <h3 className="text-xl font-semibold font-sans text-text-base mb-4">
                    {service.title}
                  </h3>
                  <p className="text-text-base opacity-80 leading-relaxed">
                    {service.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold font-sans mb-6">
            Prenota il Tuo Servizio
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Contattaci per prenotare uno dei nostri servizi specializzati. 
            Il nostro team è pronto ad offrirti la migliore assistenza professionale.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="accent"
              size="lg"
              onClick={() => window.location.href = '/contatti'}
            >
              Prenota Ora
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={() => window.location.href = 'tel:06123456789'}
              className="border-white text-white hover:bg-white hover:text-primary"
            >
              Chiama: 06 123 456 789
            </Button>
          </div>
        </div>
      </section>
    </>
  );
}
